{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.28.2", "@tailwindcss/vite": "^4.1.3", "humanize-duration": "^3.32.1", "quill": "^2.0.3", "rc-progress": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "react-simple-star-rating": "^5.1.7", "react-youtube": "^10.1.0", "uniqid": "^5.4.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tailwindcss": "^3.4.17", "vite": "^6.2.0"}}