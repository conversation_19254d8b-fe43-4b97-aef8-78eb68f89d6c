 /** @type {import('tailwindcss').Config} */
export default {
   content: ["./src/**/*.{html,js}", "./src/**/*.{jsx}"],
   theme: {
     extend: {
      fontSize:{
        'course-details-heading-small' : ['26px','36px'],
        'course-details-heading-large' : ['36px','44px'],
        'home-heading-small' : ['28px','34px'],
        'home-heading-large' : ['48px','56px'],
        'default' : ['15px','21px']
      },
      gridTemplateColumns:{
        'auto': 'repeat(auto-fit, minmax(200 px, 1fr))'
      }
     },
   },
   plugins: [],
 }
