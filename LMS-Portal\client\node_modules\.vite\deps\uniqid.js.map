{"version": 3, "sources": ["browser-external:os", "../../uniqid/index.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"os\" has been externalized for browser compatibility. Cannot access \"os.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/* \n(The MIT License)\nCopyright (c) 2014-2021 <PERSON><PERSON><PERSON> <<EMAIL>>\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n//  Unique Hexatridecimal ID Generator\n// ================================================\n\n//  Dependencies\n// ================================================\nvar pid = typeof process !== 'undefined' && process.pid ? process.pid.toString(36) : '' ;\nvar address = '';\nif(typeof __webpack_require__ !== 'function' && typeof require !== 'undefined'){\n    var mac = '', os = require('os'); \n    if(os.networkInterfaces) var networkInterfaces = os.networkInterfaces();\n    if(networkInterfaces){\n        loop:\n        for(let interface_key in networkInterfaces){\n            const networkInterface = networkInterfaces[interface_key];\n            const length = networkInterface.length;\n            for(var i = 0; i < length; i++){\n                if(networkInterface[i] !== undefined && networkInterface[i].mac && networkInterface[i].mac != '00:00:00:00:00:00'){\n                    mac = networkInterface[i].mac; break loop;\n                }\n            }\n        }\n        address = mac ? parseInt(mac.replace(/\\:|\\D+/gi, '')).toString(36) : '' ;\n    }\n} \n\n//  Exports\n// ================================================\nmodule.exports = module.exports.default = function(prefix, suffix){ return (prefix ? prefix : '') + address + pid + now().toString(36) + (suffix ? suffix : ''); }\nmodule.exports.process = function(prefix, suffix){ return (prefix ? prefix : '') + pid + now().toString(36) + (suffix ? suffix : ''); }\nmodule.exports.time    = function(prefix, suffix){ return (prefix ? prefix : '') + now().toString(36) + (suffix ? suffix : ''); }\n\n//  Helpers\n// ================================================\nfunction now(){\n    var time = Date.now();\n    var last = now.last || time;\n    return now.last = time > last ? time : last + 1;\n}\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,kFAAkF,GAAG,mIAAmI;AAAA,QACvO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAaA,QAAI,MAAM,OAAO,YAAY,eAAe,QAAQ,MAAM,QAAQ,IAAI,SAAS,EAAE,IAAI;AACrF,QAAI,UAAU;AACd,QAAG,OAAO,wBAAwB,cAAc,OAAO,cAAY,aAAY;AACvE,YAAM,IAAI,KAAK;AACnB,UAAG,GAAG,kBAAuB,qBAAoB,GAAG,kBAAkB;AACtE,UAAG,mBAAkB;AACjB;AACA,mBAAQ,iBAAiB,mBAAkB;AACvC,kBAAM,mBAAmB,kBAAkB,aAAa;AACxD,kBAAM,SAAS,iBAAiB;AAChC,iBAAQ,IAAI,GAAG,IAAI,QAAQ,KAAI;AAC3B,kBAAG,iBAAiB,CAAC,MAAM,UAAa,iBAAiB,CAAC,EAAE,OAAO,iBAAiB,CAAC,EAAE,OAAO,qBAAoB;AAC9G,sBAAM,iBAAiB,CAAC,EAAE;AAAK,sBAAM;AAAA,cACzC;AAAA,YACJ;AAAA,UACJ;AACA,kBAAU,MAAM,SAAS,IAAI,QAAQ,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI;AAAA,MACzE;AAAA,IACJ;AAfQ;AAAU;AACe;AAMb;AAYpB,WAAO,UAAU,OAAO,QAAQ,UAAU,SAAS,QAAQ,QAAO;AAAE,cAAQ,SAAS,SAAS,MAAM,UAAU,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAAK;AACjK,WAAO,QAAQ,UAAU,SAAS,QAAQ,QAAO;AAAE,cAAQ,SAAS,SAAS,MAAM,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAAK;AACtI,WAAO,QAAQ,OAAU,SAAS,QAAQ,QAAO;AAAE,cAAQ,SAAS,SAAS,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,IAAK;AAIhI,aAAS,MAAK;AACV,UAAI,OAAO,KAAK,IAAI;AACpB,UAAI,OAAO,IAAI,QAAQ;AACvB,aAAO,IAAI,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,IAClD;AAAA;AAAA;", "names": []}