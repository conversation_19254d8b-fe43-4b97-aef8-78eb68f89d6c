"use client";
var Xt=Object.create;var K=Object.defineProperty;var Qt=Object.getOwnPropertyDescriptor;var Wt=Object.getOwnPropertyNames;var Gt=Object.getPrototypeOf,qt=Object.prototype.hasOwnProperty;var Kt=(t,o)=>{for(var e in o)K(t,e,{get:o[e],enumerable:!0})},Ct=(t,o,e,r)=>{if(o&&typeof o=="object"||typeof o=="function")for(let a of Wt(o))!qt.call(t,a)&&a!==e&&K(t,a,{get:()=>o[a],enumerable:!(r=Qt(o,a))||r.enumerable});return t};var w=(t,o,e)=>(e=t!=null?Xt(Gt(t)):{},Ct(o||!t||!t.__esModule?K(e,"default",{value:t,enumerable:!0}):e,t)),Yt=t=>Ct(K({},"__esModule",{value:!0}),t);var po={};Kt(po,{Bounce:()=>lt,Flip:()=>Vt,Icons:()=>G,Slide:()=>Ut,ToastContainer:()=>bt,Zoom:()=>Ht,collapseToast:()=>Y,cssTransition:()=>F,toast:()=>y});module.exports=Yt(po);var xt=require("react"),z=t=>typeof t=="number"&&!isNaN(t),D=t=>typeof t=="string",P=t=>typeof t=="function",Et=t=>D(t)||z(t),V=t=>D(t)||P(t)?t:null,Pt=(t,o)=>t===!1||z(t)&&t>0?t:o,X=t=>(0,xt.isValidElement)(t)||D(t)||P(t)||z(t);var M=w(require("react"));function Y(t,o,e=300){let{scrollHeight:r,style:a}=t;requestAnimationFrame(()=>{a.minHeight="initial",a.height=r+"px",a.transition=`all ${e}ms`,requestAnimationFrame(()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(o,e)})})}function F({enter:t,exit:o,appendPosition:e=!1,collapse:r=!0,collapseDuration:a=300}){return function({children:s,position:d,preventExitTransition:f,done:T,nodeRef:g,isIn:v,playToast:x}){let C=e?`${t}--${d}`:t,k=e?`${o}--${d}`:o,E=(0,M.useRef)(0);return(0,M.useLayoutEffect)(()=>{let c=g.current,p=C.split(" "),b=n=>{n.target===g.current&&(x(),c.removeEventListener("animationend",b),c.removeEventListener("animationcancel",b),E.current===0&&n.type!=="animationcancel"&&c.classList.remove(...p))};(()=>{c.classList.add(...p),c.addEventListener("animationend",b),c.addEventListener("animationcancel",b)})()},[]),(0,M.useEffect)(()=>{let c=g.current,p=()=>{c.removeEventListener("animationend",p),r?Y(c,T,a):T()};v||(f?p():(()=>{E.current=1,c.className+=` ${k}`,c.addEventListener("animationend",p)})())},[v]),M.default.createElement(M.default.Fragment,null,s)}}var j=require("react");function dt(t,o){return{content:mt(t.content,t.props),containerId:t.props.containerId,id:t.props.toastId,theme:t.props.theme,type:t.props.type,data:t.props.data||{},isLoading:t.props.isLoading,icon:t.props.icon,reason:t.removalReason,status:o}}function mt(t,o,e=!1){return(0,j.isValidElement)(t)&&!D(t.type)?(0,j.cloneElement)(t,{closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):P(t)?t({closeToast:o.closeToast,toastProps:o,data:o.data,isPaused:e}):t}var Z=w(require("react"));function It({closeToast:t,theme:o,ariaLabel:e="close"}){return Z.default.createElement("button",{className:`Toastify__close-button Toastify__close-button--${o}`,type:"button",onClick:r=>{r.stopPropagation(),t(!0)},"aria-label":e},Z.default.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},Z.default.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}var J=w(require("react")),pt=w(require("clsx"));function St({delay:t,isRunning:o,closeToast:e,type:r="default",hide:a,className:l,controlledProgress:s,progress:d,rtl:f,isIn:T,theme:g}){let v=a||s&&d===0,x={animationDuration:`${t}ms`,animationPlayState:o?"running":"paused"};s&&(x.transform=`scaleX(${d})`);let C=(0,pt.default)("Toastify__progress-bar",s?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${r}`,{["Toastify__progress-bar--rtl"]:f}),k=P(l)?l({rtl:f,type:r,defaultClassName:C}):(0,pt.default)(C,l),E={[s&&d>=1?"onTransitionEnd":"onAnimationEnd"]:s&&d<1?null:()=>{T&&e()}};return J.default.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":v},J.default.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${r}`}),J.default.createElement("div",{role:"progressbar","aria-hidden":v?"true":"false","aria-label":"notification timer",className:k,style:x,...E}))}var _t=w(require("clsx")),N=w(require("react"));var Zt=1,ut=()=>`${Zt++}`;function kt(t,o,e){let r=1,a=0,l=[],s=[],d=o,f=new Map,T=new Set,g=i=>(T.add(i),()=>T.delete(i)),v=()=>{s=Array.from(f.values()),T.forEach(i=>i())},x=({containerId:i,toastId:n,updateId:u})=>{let h=i?i!==t:t!==1,m=f.has(n)&&u==null;return h||m},C=(i,n)=>{f.forEach(u=>{var h;(n==null||n===u.props.toastId)&&((h=u.toggle)==null||h.call(u,i))})},k=i=>{var n,u;(u=(n=i.props)==null?void 0:n.onClose)==null||u.call(n,i.removalReason),i.isActive=!1},E=i=>{if(i==null)f.forEach(k);else{let n=f.get(i);n&&k(n)}v()},c=()=>{a-=l.length,l=[]},p=i=>{var m,_;let{toastId:n,updateId:u}=i.props,h=u==null;i.staleId&&f.delete(i.staleId),i.isActive=!0,f.set(n,i),v(),e(dt(i,h?"added":"updated")),h&&((_=(m=i.props).onOpen)==null||_.call(m))};return{id:t,props:d,observe:g,toggle:C,removeToast:E,toasts:f,clearQueue:c,buildToast:(i,n)=>{if(x(n))return;let{toastId:u,updateId:h,data:m,staleId:_,delay:A}=n,U=h==null;U&&a++;let O={...d,style:d.toastStyle,key:r++,...Object.fromEntries(Object.entries(n).filter(([R,ft])=>ft!=null)),toastId:u,updateId:h,data:m,isIn:!1,className:V(n.className||d.toastClassName),progressClassName:V(n.progressClassName||d.progressClassName),autoClose:n.isLoading?!1:Pt(n.autoClose,d.autoClose),closeToast(R){f.get(u).removalReason=R,E(u)},deleteToast(){let R=f.get(u);if(R!=null){if(e(dt(R,"removed")),f.delete(u),a--,a<0&&(a=0),l.length>0){p(l.shift());return}v()}}};O.closeButton=d.closeButton,n.closeButton===!1||X(n.closeButton)?O.closeButton=n.closeButton:n.closeButton===!0&&(O.closeButton=X(d.closeButton)?d.closeButton:!0);let H={content:i,props:O,staleId:_};d.limit&&d.limit>0&&a>d.limit&&U?l.push(H):z(A)?setTimeout(()=>{p(H)},A):p(H)},setProps(i){d=i},setToggle:(i,n)=>{let u=f.get(i);u&&(u.toggle=n)},isToastActive:i=>{var n;return(n=f.get(i))==null?void 0:n.isActive},getSnapshot:()=>s}}var I=new Map,Q=[],yt=new Set,Jt=t=>yt.forEach(o=>o(t)),At=()=>I.size>0;function to(){Q.forEach(t=>Tt(t.content,t.options)),Q=[]}var Ot=(t,{containerId:o})=>{var e;return(e=I.get(o||1))==null?void 0:e.toasts.get(t)};function tt(t,o){var r;if(o)return!!((r=I.get(o))!=null&&r.isToastActive(t));let e=!1;return I.forEach(a=>{a.isToastActive(t)&&(e=!0)}),e}function Nt(t){if(!At()){Q=Q.filter(o=>t!=null&&o.options.toastId!==t);return}if(t==null||Et(t))I.forEach(o=>{o.removeToast(t)});else if(t&&("containerId"in t||"id"in t)){let o=I.get(t.containerId);o?o.removeToast(t.id):I.forEach(e=>{e.removeToast(t.id)})}}var wt=(t={})=>{I.forEach(o=>{o.props.limit&&(!t.containerId||o.id===t.containerId)&&o.clearQueue()})};function Tt(t,o){X(t)&&(At()||Q.push({content:t,options:o}),I.forEach(e=>{e.buildToast(t,o)}))}function Dt(t){var o;(o=I.get(t.containerId||1))==null||o.setToggle(t.id,t.fn)}function gt(t,o){I.forEach(e=>{(o==null||!(o!=null&&o.containerId)||(o==null?void 0:o.containerId)===e.id)&&e.toggle(t,o==null?void 0:o.id)})}function Lt(t){let o=t.containerId||1;return{subscribe(e){let r=kt(o,t,Jt);I.set(o,r);let a=r.observe(e);return to(),()=>{a(),I.delete(o)}},setProps(e){var r;(r=I.get(o))==null||r.setProps(e)},getSnapshot(){var e;return(e=I.get(o))==null?void 0:e.getSnapshot()}}}function Mt(t){return yt.add(t),()=>{yt.delete(t)}}function oo(t){return t&&(D(t.toastId)||z(t.toastId))?t.toastId:ut()}function W(t,o){return Tt(t,o),o.toastId}function ot(t,o){return{...o,type:o&&o.type||t,toastId:oo(o)}}function et(t){return(o,e)=>W(o,ot(t,e))}function y(t,o){return W(t,ot("default",o))}y.loading=(t,o)=>W(t,ot("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...o}));function eo(t,{pending:o,error:e,success:r},a){let l;o&&(l=D(o)?y.loading(o,a):y.loading(o.render,{...a,...o}));let s={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},d=(T,g,v)=>{if(g==null){y.dismiss(l);return}let x={type:T,...s,...a,data:v},C=D(g)?{render:g}:g;return l?y.update(l,{...x,...C}):y(C.render,{...x,...C}),v},f=P(t)?t():t;return f.then(T=>d("success",r,T)).catch(T=>d("error",e,T)),f}y.promise=eo;y.success=et("success");y.info=et("info");y.error=et("error");y.warning=et("warning");y.warn=y.warning;y.dark=(t,o)=>W(t,ot("default",{theme:"dark",...o}));function ao(t){Nt(t)}y.dismiss=ao;y.clearWaitingQueue=wt;y.isActive=tt;y.update=(t,o={})=>{let e=Ot(t,o);if(e){let{props:r,content:a}=e,l={delay:100,...r,...o,toastId:o.toastId||t,updateId:ut()};l.toastId!==t&&(l.staleId=t);let s=l.render||a;delete l.render,W(s,l)}};y.done=t=>{y.update(t,{progress:1})};y.onChange=Mt;y.play=t=>gt(!0,t);y.pause=t=>gt(!1,t);var at=require("react");function $t(t){var s;let{subscribe:o,getSnapshot:e,setProps:r}=(0,at.useRef)(Lt(t)).current;r(t);let a=(s=(0,at.useSyncExternalStore)(o,e,e))==null?void 0:s.slice();function l(d){if(!a)return[];let f=new Map;return t.newestOnTop&&a.reverse(),a.forEach(T=>{let{position:g}=T.props;f.has(g)||f.set(g,[]),f.get(g).push(T)}),Array.from(f,T=>d(T[0],T[1]))}return{getToastToRender:l,isToastActive:tt,count:a==null?void 0:a.length}}var B=require("react");function Rt(t){let[o,e]=(0,B.useState)(!1),[r,a]=(0,B.useState)(!1),l=(0,B.useRef)(null),s=(0,B.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:d,pauseOnHover:f,closeToast:T,onClick:g,closeOnClick:v}=t;Dt({id:t.toastId,containerId:t.containerId,fn:e}),(0,B.useEffect)(()=>{if(t.pauseOnFocusLoss)return x(),()=>{C()}},[t.pauseOnFocusLoss]);function x(){document.hasFocus()||p(),window.addEventListener("focus",c),window.addEventListener("blur",p)}function C(){window.removeEventListener("focus",c),window.removeEventListener("blur",p)}function k(m){if(t.draggable===!0||t.draggable===m.pointerType){b();let _=l.current;s.canCloseOnClick=!0,s.canDrag=!0,_.style.transition="none",t.draggableDirection==="x"?(s.start=m.clientX,s.removalDistance=_.offsetWidth*(t.draggablePercent/100)):(s.start=m.clientY,s.removalDistance=_.offsetHeight*(t.draggablePercent===80?t.draggablePercent*1.5:t.draggablePercent)/100)}}function E(m){let{top:_,bottom:A,left:U,right:O}=l.current.getBoundingClientRect();m.nativeEvent.type!=="touchend"&&t.pauseOnHover&&m.clientX>=U&&m.clientX<=O&&m.clientY>=_&&m.clientY<=A?p():c()}function c(){e(!0)}function p(){e(!1)}function b(){s.didMove=!1,document.addEventListener("pointermove",n),document.addEventListener("pointerup",u)}function i(){document.removeEventListener("pointermove",n),document.removeEventListener("pointerup",u)}function n(m){let _=l.current;if(s.canDrag&&_){s.didMove=!0,o&&p(),t.draggableDirection==="x"?s.delta=m.clientX-s.start:s.delta=m.clientY-s.start,s.start!==m.clientX&&(s.canCloseOnClick=!1);let A=t.draggableDirection==="x"?`${s.delta}px, var(--y)`:`0, calc(${s.delta}px + var(--y))`;_.style.transform=`translate3d(${A},0)`,_.style.opacity=`${1-Math.abs(s.delta/s.removalDistance)}`}}function u(){i();let m=l.current;if(s.canDrag&&s.didMove&&m){if(s.canDrag=!1,Math.abs(s.delta)>s.removalDistance){a(!0),t.closeToast(!0),t.collapseAll();return}m.style.transition="transform 0.2s, opacity 0.2s",m.style.removeProperty("transform"),m.style.removeProperty("opacity")}}let h={onPointerDown:k,onPointerUp:E};return d&&f&&(h.onMouseEnter=p,t.stacked||(h.onMouseLeave=c)),v&&(h.onClick=m=>{g&&g(m),s.canCloseOnClick&&T(!0)}),{playToast:c,pauseToast:p,isRunning:o,preventExitTransition:r,toastRef:l,eventHandlers:h}}var st=require("react"),Bt=typeof window!="undefined"?st.useLayoutEffect:st.useEffect;var nt=w(require("clsx")),$=w(require("react"));var S=w(require("react"));var rt=({theme:t,type:o,isLoading:e,...r})=>S.default.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:t==="colored"?"currentColor":`var(--toastify-icon-color-${o})`,...r});function ro(t){return S.default.createElement(rt,{...t},S.default.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))}function no(t){return S.default.createElement(rt,{...t},S.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))}function io(t){return S.default.createElement(rt,{...t},S.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))}function lo(t){return S.default.createElement(rt,{...t},S.default.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))}function fo(){return S.default.createElement("div",{className:"Toastify__spinner"})}var G={info:no,warning:ro,success:io,error:lo,spinner:fo},co=t=>t in G;function zt({theme:t,type:o,isLoading:e,icon:r}){let a=null,l={theme:t,type:o};return r===!1||(P(r)?a=r({...l,isLoading:e}):(0,S.isValidElement)(r)?a=(0,S.cloneElement)(r,l):e?a=G.spinner():co(o)&&(a=G[o](l))),a}var Ft=t=>{let{isRunning:o,preventExitTransition:e,toastRef:r,eventHandlers:a,playToast:l}=Rt(t),{closeButton:s,children:d,autoClose:f,onClick:T,type:g,hideProgressBar:v,closeToast:x,transition:C,position:k,className:E,style:c,progressClassName:p,updateId:b,role:i,progress:n,rtl:u,toastId:h,deleteToast:m,isIn:_,isLoading:A,closeOnClick:U,theme:O,ariaLabel:H}=t,R=(0,nt.default)("Toastify__toast",`Toastify__toast-theme--${O}`,`Toastify__toast--${g}`,{["Toastify__toast--rtl"]:u},{["Toastify__toast--close-on-click"]:U}),ft=P(E)?E({rtl:u,position:k,type:g,defaultClassName:R}):(0,nt.default)(R,E),vt=zt(t),ht=!!n||!f,ct={closeToast:x,type:g,theme:O},q=null;return s===!1||(P(s)?q=s(ct):(0,$.isValidElement)(s)?q=(0,$.cloneElement)(s,ct):q=It(ct)),$.default.createElement(C,{isIn:_,done:m,position:k,preventExitTransition:e,nodeRef:r,playToast:l},$.default.createElement("div",{id:h,tabIndex:0,onClick:T,"data-in":_,className:ft,...a,style:c,ref:r,..._&&{role:i,"aria-label":H}},vt!=null&&$.default.createElement("div",{className:(0,nt.default)("Toastify__toast-icon",{["Toastify--animate-icon Toastify__zoom-enter"]:!A})},vt),mt(d,t,!o),q,!t.customProgressBar&&$.default.createElement(St,{...b&&!ht?{key:`p-${b}`}:{},rtl:u,theme:O,delay:f,isRunning:o,isIn:_,closeToast:x,hide:v,type:g,className:p,controlledProgress:ht,progress:n||0})))};var it=(t,o=!1)=>({enter:`Toastify--animate Toastify__${t}-enter`,exit:`Toastify--animate Toastify__${t}-exit`,appendPosition:o}),lt=F(it("bounce",!0)),Ut=F(it("slide",!0)),Ht=F(it("zoom")),Vt=F(it("flip"));var mo={position:"top-right",transition:lt,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:t=>t.altKey&&t.code==="KeyT"};function bt(t){let o={...mo,...t},e=t.stacked,[r,a]=(0,N.useState)(!0),l=(0,N.useRef)(null),{getToastToRender:s,isToastActive:d,count:f}=$t(o),{className:T,style:g,rtl:v,containerId:x,hotKeys:C}=o;function k(c){let p=(0,_t.default)("Toastify__toast-container",`Toastify__toast-container--${c}`,{["Toastify__toast-container--rtl"]:v});return P(T)?T({position:c,rtl:v,defaultClassName:p}):(0,_t.default)(p,V(T))}function E(){e&&(a(!0),y.play())}return Bt(()=>{var c;if(e){let p=l.current.querySelectorAll('[data-in="true"]'),b=12,i=(c=o.position)==null?void 0:c.includes("top"),n=0,u=0;Array.from(p).reverse().forEach((h,m)=>{let _=h;_.classList.add("Toastify__toast--stacked"),m>0&&(_.dataset.collapsed=`${r}`),_.dataset.pos||(_.dataset.pos=i?"top":"bot");let A=n*(r?.2:1)+(r?0:b*m);_.style.setProperty("--y",`${i?A:A*-1}px`),_.style.setProperty("--g",`${b}`),_.style.setProperty("--s",`${1-(r?u:0)}`),n+=_.offsetHeight,u+=.025})}},[r,f,e]),(0,N.useEffect)(()=>{function c(p){var i;let b=l.current;C(p)&&((i=b.querySelector('[tabIndex="0"]'))==null||i.focus(),a(!1),y.pause()),p.key==="Escape"&&(document.activeElement===b||b!=null&&b.contains(document.activeElement))&&(a(!0),y.play())}return document.addEventListener("keydown",c),()=>{document.removeEventListener("keydown",c)}},[C]),N.default.createElement("section",{ref:l,className:"Toastify",id:x,onMouseEnter:()=>{e&&(a(!1),y.pause())},onMouseLeave:E,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":o["aria-label"]},s((c,p)=>{let b=p.length?{...g}:{...g,pointerEvents:"none"};return N.default.createElement("div",{tabIndex:-1,className:k(c),"data-stacked":e,style:b,key:`c-${c}`},p.map(({content:i,props:n})=>N.default.createElement(Ft,{...n,stacked:e,collapseAll:E,isIn:d(n.toastId,n.containerId),key:`t-${n.key}`},i)))}))}0&&(module.exports={Bounce,Flip,Icons,Slide,ToastContainer,Zoom,collapseToast,cssTransition,toast});
//# sourceMappingURL=unstyled.js.map