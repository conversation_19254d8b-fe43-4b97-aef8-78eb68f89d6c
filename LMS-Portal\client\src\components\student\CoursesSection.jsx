import React, { useContext } from 'react'
import { <PERSON> } from 'react-router-dom'
import { AppContext } from '../../context/AppContext'
import CourseCard from './CourseCard'

function CoursesSection() {

  const {allcourses} = useContext(AppContext)

  return (
    <div className='py-16 md:px-40 px-8'>
      <h2 className='text-3xl font-medium text-gray-800'>Learn From the Best</h2>
      <p className='text-sm md:text-base text-gray-500 mt-3'>Discover our top-rated courses across various categories. From coding and design to business and wellness, our courses are crafted to deliver results.</p>

      <div>
        {allcourses.slice(0,4).map((course, index)=><CourseCard key={index} course={course} />)}
      </div>


    <Link to={'/course-list'} onClick={()=>scrollTo(0,0)} className='text-gray-500 border border-gray-500/30 px-10 py-3 rounded'> Show all courses</Link>
    </div>
  )
}

export default CoursesSection