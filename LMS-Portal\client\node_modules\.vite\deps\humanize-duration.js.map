{"version": 3, "sources": ["../../humanize-duration/humanize-duration.js"], "sourcesContent": ["// HumanizeDuration.js - https://git.io/j0HgmQ\n\n// @ts-check\n\n/**\n * @typedef {string | ((unitCount: number) => string)} Unit\n */\n\n/**\n * @typedef {(\"y\" | \"mo\" | \"w\" | \"d\" | \"h\" | \"m\" | \"s\" | \"ms\")} UnitName\n */\n\n/**\n * @typedef {Object} UnitMeasures\n * @prop {number} y\n * @prop {number} mo\n * @prop {number} w\n * @prop {number} d\n * @prop {number} h\n * @prop {number} m\n * @prop {number} s\n * @prop {number} ms\n */\n\n/**\n * @internal\n * @typedef {[string, string, string, string, string, string, string, string, string, string]} DigitReplacements\n */\n\n/**\n * @typedef {Object} Language\n * @prop {Unit} y\n * @prop {Unit} mo\n * @prop {Unit} w\n * @prop {Unit} d\n * @prop {Unit} h\n * @prop {Unit} m\n * @prop {Unit} s\n * @prop {Unit} ms\n * @prop {string} [decimal]\n * @prop {string} [delimiter]\n * @prop {DigitReplacements} [_digitReplacements]\n * @prop {boolean} [_numberFirst]\n * @prop {boolean} [_hideCountIf2]\n */\n\n/**\n * @typedef {Object} Options\n * @prop {string} [language]\n * @prop {Record<string, Language>} [languages]\n * @prop {string[]} [fallbacks]\n * @prop {string} [delimiter]\n * @prop {string} [spacer]\n * @prop {boolean} [round]\n * @prop {number} [largest]\n * @prop {UnitName[]} [units]\n * @prop {string} [decimal]\n * @prop {string} [conjunction]\n * @prop {number} [maxDecimalPoints]\n * @prop {UnitMeasures} [unitMeasures]\n * @prop {boolean} [serialComma]\n * @prop {DigitReplacements} [digitReplacements]\n */\n\n/**\n * @internal\n * @typedef {Required<Options>} NormalizedOptions\n */\n\n(function () {\n  // Fallback for `Object.assign` if relevant.\n  var assign =\n    Object.assign ||\n    /** @param {...any} destination */\n    function (destination) {\n      var source;\n      for (var i = 1; i < arguments.length; i++) {\n        source = arguments[i];\n        for (var prop in source) {\n          if (has(source, prop)) {\n            destination[prop] = source[prop];\n          }\n        }\n      }\n      return destination;\n    };\n\n  // Fallback for `Array.isArray` if relevant.\n  var isArray =\n    Array.isArray ||\n    function (arg) {\n      return Object.prototype.toString.call(arg) === \"[object Array]\";\n    };\n\n  // This has to be defined separately because of a bug: we want to alias\n  // `gr` and `el` for backwards-compatiblity. In a breaking change, we can\n  // remove `gr` entirely.\n  // See https://github.com/EvanHahn/HumanizeDuration.js/issues/143 for more.\n  var GREEK = language(\n    function (c) {\n      return c === 1 ? \"χρόνος\" : \"χρόνια\";\n    },\n    function (c) {\n      return c === 1 ? \"μήνας\" : \"μήνες\";\n    },\n    function (c) {\n      return c === 1 ? \"εβδομάδα\" : \"εβδομάδες\";\n    },\n    function (c) {\n      return c === 1 ? \"μέρα\" : \"μέρες\";\n    },\n    function (c) {\n      return c === 1 ? \"ώρα\" : \"ώρες\";\n    },\n    function (c) {\n      return c === 1 ? \"λεπτό\" : \"λεπτά\";\n    },\n    function (c) {\n      return c === 1 ? \"δευτερόλεπτο\" : \"δευτερόλεπτα\";\n    },\n    function (c) {\n      return (c === 1 ? \"χιλιοστό\" : \"χιλιοστά\") + \" του δευτερολέπτου\";\n    },\n    \",\"\n  );\n\n  /**\n   * @internal\n   * @type {Record<string, Language>}\n   */\n  var LANGUAGES = {\n    af: language(\n      \"jaar\",\n      function (c) {\n        return \"maand\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weke\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dae\";\n      },\n      function (c) {\n        return c === 1 ? \"uur\" : \"ure\";\n      },\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minute\";\n      },\n      function (c) {\n        return \"sekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisekonde\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    am: language(\"ዓመት\", \"ወር\", \"ሳምንት\", \"ቀን\", \"ሰዓት\", \"ደቂቃ\", \"ሰከንድ\", \"ሚሊሰከንድ\"),\n    ar: assign(\n      language(\n        function (c) {\n          return [\"سنة\", \"سنتان\", \"سنوات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"شهر\", \"شهران\", \"أشهر\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"أسبوع\", \"أسبوعين\", \"أسابيع\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"يوم\", \"يومين\", \"أيام\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ساعة\", \"ساعتين\", \"ساعات\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"دقيقة\", \"دقيقتان\", \"دقائق\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"ثانية\", \"ثانيتان\", \"ثواني\"][getArabicForm(c)];\n        },\n        function (c) {\n          return [\"جزء من الثانية\", \"جزآن من الثانية\", \"أجزاء من الثانية\"][\n            getArabicForm(c)\n          ];\n        },\n        \",\"\n      ),\n      {\n        delimiter: \" ﻭ \",\n        _hideCountIf2: true,\n        _digitReplacements: [\"۰\", \"١\", \"٢\", \"٣\", \"٤\", \"٥\", \"٦\", \"٧\", \"٨\", \"٩\"]\n      }\n    ),\n    bg: language(\n      function (c) {\n        return [\"години\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеца\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"седмици\", \"седмица\", \"седмици\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дни\", \"ден\", \"дни\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часа\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минути\", \"минута\", \"минути\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    bn: language(\n      \"বছর\",\n      \"মাস\",\n      \"সপ্তাহ\",\n      \"দিন\",\n      \"ঘন্টা\",\n      \"মিনিট\",\n      \"সেকেন্ড\",\n      \"মিলিসেকেন্ড\"\n    ),\n    ca: language(\n      function (c) {\n        return \"any\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"os\");\n      },\n      function (c) {\n        return \"setman\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"di\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"hor\" + (c === 1 ? \"a\" : \"es\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segon\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegon\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ckb: language(\n      \"ساڵ\",\n      \"مانگ\",\n      \"هەفتە\",\n      \"ڕۆژ\",\n      \"کاژێر\",\n      \"خولەک\",\n      \"چرکە\",\n      \"میلی چرکە\",\n      \".\"\n    ),\n    cs: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"roky\", \"let\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"měsíc\", \"měsíce\", \"měsíce\", \"měsíců\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"týden\", \"týdne\", \"týdny\", \"týdnů\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"den\", \"dne\", \"dny\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodin\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    cy: language(\n      \"flwyddyn\",\n      \"mis\",\n      \"wythnos\",\n      \"diwrnod\",\n      \"awr\",\n      \"munud\",\n      \"eiliad\",\n      \"milieiliad\"\n    ),\n    da: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uge\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"ter\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    de: language(\n      function (c) {\n        return \"Jahr\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Monat\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Woche\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Tag\" + (c === 1 ? \"\" : \"e\");\n      },\n      function (c) {\n        return \"Stunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Minute\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Sekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      function (c) {\n        return \"Millisekunde\" + (c === 1 ? \"\" : \"n\");\n      },\n      \",\"\n    ),\n    el: GREEK,\n    en: language(\n      function (c) {\n        return \"year\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"month\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"week\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"day\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hour\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minute\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"\" : \"s\");\n      }\n    ),\n    eo: language(\n      function (c) {\n        return \"jaro\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"monato\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"semajno\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"tago\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"horo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"sekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      function (c) {\n        return \"milisekundo\" + (c === 1 ? \"\" : \"j\");\n      },\n      \",\"\n    ),\n    es: language(\n      function (c) {\n        return \"año\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"\" : \"es\");\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"día\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milisegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    et: language(\n      function (c) {\n        return \"aasta\" + (c === 1 ? \"\" : \"t\");\n      },\n      function (c) {\n        return \"kuu\" + (c === 1 ? \"\" : \"d\");\n      },\n      function (c) {\n        return \"nädal\" + (c === 1 ? \"\" : \"at\");\n      },\n      function (c) {\n        return \"päev\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"tund\" + (c === 1 ? \"\" : \"i\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"it\");\n      },\n      \",\"\n    ),\n    eu: language(\n      \"urte\",\n      \"hilabete\",\n      \"aste\",\n      \"egun\",\n      \"ordu\",\n      \"minutu\",\n      \"segundo\",\n      \"milisegundo\",\n      \",\"\n    ),\n    fa: language(\n      \"سال\",\n      \"ماه\",\n      \"هفته\",\n      \"روز\",\n      \"ساعت\",\n      \"دقیقه\",\n      \"ثانیه\",\n      \"میلی ثانیه\"\n    ),\n    fi: language(\n      function (c) {\n        return c === 1 ? \"vuosi\" : \"vuotta\";\n      },\n      function (c) {\n        return c === 1 ? \"kuukausi\" : \"kuukautta\";\n      },\n      function (c) {\n        return \"viikko\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"päivä\" + (c === 1 ? \"\" : \"ä\");\n      },\n      function (c) {\n        return \"tunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"minuutti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"sekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      function (c) {\n        return \"millisekunti\" + (c === 1 ? \"\" : \"a\");\n      },\n      \",\"\n    ),\n    fo: language(\n      \"ár\",\n      function (c) {\n        return c === 1 ? \"mánaður\" : \"mánaðir\";\n      },\n      function (c) {\n        return c === 1 ? \"vika\" : \"vikur\";\n      },\n      function (c) {\n        return c === 1 ? \"dagur\" : \"dagar\";\n      },\n      function (c) {\n        return c === 1 ? \"tími\" : \"tímar\";\n      },\n      function (c) {\n        return c === 1 ? \"minuttur\" : \"minuttir\";\n      },\n      \"sekund\",\n      \"millisekund\",\n      \",\"\n    ),\n    fr: language(\n      function (c) {\n        return \"an\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \"mois\",\n      function (c) {\n        return \"semaine\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"jour\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"heure\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"minute\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"seconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      function (c) {\n        return \"milliseconde\" + (c >= 2 ? \"s\" : \"\");\n      },\n      \",\"\n    ),\n    gr: GREEK,\n    he: language(\n      function (c) {\n        return c === 1 ? \"שנה\" : \"שנים\";\n      },\n      function (c) {\n        return c === 1 ? \"חודש\" : \"חודשים\";\n      },\n      function (c) {\n        return c === 1 ? \"שבוע\" : \"שבועות\";\n      },\n      function (c) {\n        return c === 1 ? \"יום\" : \"ימים\";\n      },\n      function (c) {\n        return c === 1 ? \"שעה\" : \"שעות\";\n      },\n      function (c) {\n        return c === 1 ? \"דקה\" : \"דקות\";\n      },\n      function (c) {\n        return c === 1 ? \"שניה\" : \"שניות\";\n      },\n      function (c) {\n        return c === 1 ? \"מילישנייה\" : \"מילישניות\";\n      }\n    ),\n    hr: language(\n      function (c) {\n        if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"godine\";\n        }\n        return \"godina\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"mjesec\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"mjeseca\";\n        }\n        return \"mjeseci\";\n      },\n      function (c) {\n        if (c % 10 === 1 && c !== 11) {\n          return \"tjedan\";\n        }\n        return \"tjedna\";\n      },\n      function (c) {\n        return c === 1 ? \"dan\" : \"dana\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"sat\";\n        } else if (c === 2 || c === 3 || c === 4) {\n          return \"sata\";\n        }\n        return \"sati\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if ((mod10 === 2 || mod10 === 3 || mod10 === 4) && (c < 10 || c > 14)) {\n          return \"minute\";\n        }\n        return \"minuta\";\n      },\n      function (c) {\n        var mod10 = c % 10;\n        if (mod10 === 5 || (Math.floor(c) === c && c >= 10 && c <= 19)) {\n          return \"sekundi\";\n        } else if (mod10 === 1) {\n          return \"sekunda\";\n        } else if (mod10 === 2 || mod10 === 3 || mod10 === 4) {\n          return \"sekunde\";\n        }\n        return \"sekundi\";\n      },\n      function (c) {\n        if (c === 1) {\n          return \"milisekunda\";\n        } else if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n          return \"milisekunde\";\n        }\n        return \"milisekundi\";\n      },\n      \",\"\n    ),\n    hi: language(\n      \"साल\",\n      function (c) {\n        return c === 1 ? \"महीना\" : \"महीने\";\n      },\n      function (c) {\n        return c === 1 ? \"हफ़्ता\" : \"हफ्ते\";\n      },\n      \"दिन\",\n      function (c) {\n        return c === 1 ? \"घंटा\" : \"घंटे\";\n      },\n      \"मिनट\",\n      \"सेकंड\",\n      \"मिलीसेकंड\"\n    ),\n    hu: language(\n      \"év\",\n      \"hónap\",\n      \"hét\",\n      \"nap\",\n      \"óra\",\n      \"perc\",\n      \"másodperc\",\n      \"ezredmásodperc\",\n      \",\"\n    ),\n    id: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"menit\",\n      \"detik\",\n      \"milidetik\"\n    ),\n    is: language(\n      \"ár\",\n      function (c) {\n        return \"mánuð\" + (c === 1 ? \"ur\" : \"ir\");\n      },\n      function (c) {\n        return \"vik\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"ur\" : \"ar\");\n      },\n      function (c) {\n        return \"klukkutím\" + (c === 1 ? \"i\" : \"ar\");\n      },\n      function (c) {\n        return \"mínút\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"sekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      },\n      function (c) {\n        return \"millisekúnd\" + (c === 1 ? \"a\" : \"ur\");\n      }\n    ),\n    it: language(\n      function (c) {\n        return \"ann\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"mes\" + (c === 1 ? \"e\" : \"i\");\n      },\n      function (c) {\n        return \"settiman\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"giorn\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"or\" + (c === 1 ? \"a\" : \"e\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"second\" + (c === 1 ? \"o\" : \"i\");\n      },\n      function (c) {\n        return \"millisecond\" + (c === 1 ? \"o\" : \"i\");\n      },\n      \",\"\n    ),\n    ja: language(\"年\", \"ヶ月\", \"週\", \"日\", \"時間\", \"分\", \"秒\", \"ミリ秒\"),\n    km: language(\n      \"ឆ្នាំ\",\n      \"ខែ\",\n      \"សប្តាហ៍\",\n      \"ថ្ងៃ\",\n      \"ម៉ោង\",\n      \"នាទី\",\n      \"វិនាទី\",\n      \"មិល្លីវិនាទី\"\n    ),\n    kn: language(\n      function (c) {\n        return c === 1 ? \"ವರ್ಷ\" : \"ವರ್ಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ತಿಂಗಳು\" : \"ತಿಂಗಳುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ವಾರ\" : \"ವಾರಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ದಿನ\" : \"ದಿನಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಗಂಟೆ\" : \"ಗಂಟೆಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ನಿಮಿಷ\" : \"ನಿಮಿಷಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಸೆಕೆಂಡ್\" : \"ಸೆಕೆಂಡುಗಳು\";\n      },\n      function (c) {\n        return c === 1 ? \"ಮಿಲಿಸೆಕೆಂಡ್\" : \"ಮಿಲಿಸೆಕೆಂಡುಗಳು\";\n      }\n    ),\n    ko: language(\"년\", \"개월\", \"주일\", \"일\", \"시간\", \"분\", \"초\", \"밀리 초\"),\n    ku: language(\n      \"sal\",\n      \"meh\",\n      \"hefte\",\n      \"roj\",\n      \"seet\",\n      \"deqe\",\n      \"saniye\",\n      \"mîlîçirk\",\n      \",\"\n    ),\n    lo: language(\n      \"ປີ\",\n      \"ເດືອນ\",\n      \"ອາທິດ\",\n      \"ມື້\",\n      \"ຊົ່ວໂມງ\",\n      \"ນາທີ\",\n      \"ວິນາທີ\",\n      \"ມິນລິວິນາທີ\",\n      \",\"\n    ),\n    lt: language(\n      function (c) {\n        return c % 10 === 0 || (c % 100 >= 10 && c % 100 <= 20)\n          ? \"metų\"\n          : \"metai\";\n      },\n      function (c) {\n        return [\"mėnuo\", \"mėnesiai\", \"mėnesių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"savaitė\", \"savaitės\", \"savaičių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"diena\", \"dienos\", \"dienų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"valanda\", \"valandos\", \"valandų\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"minutė\", \"minutės\", \"minučių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"sekundė\", \"sekundės\", \"sekundžių\"][getLithuanianForm(c)];\n      },\n      function (c) {\n        return [\"milisekundė\", \"milisekundės\", \"milisekundžių\"][\n          getLithuanianForm(c)\n        ];\n      },\n      \",\"\n    ),\n    lv: language(\n      function (c) {\n        return getLatvianForm(c) ? \"gads\" : \"gadi\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"mēnesis\" : \"mēneši\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"nedēļa\" : \"nedēļas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"diena\" : \"dienas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"stunda\" : \"stundas\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"minūte\" : \"minūtes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"sekunde\" : \"sekundes\";\n      },\n      function (c) {\n        return getLatvianForm(c) ? \"milisekunde\" : \"milisekundes\";\n      },\n      \",\"\n    ),\n    mk: language(\n      function (c) {\n        return c === 1 ? \"година\" : \"години\";\n      },\n      function (c) {\n        return c === 1 ? \"месец\" : \"месеци\";\n      },\n      function (c) {\n        return c === 1 ? \"недела\" : \"недели\";\n      },\n      function (c) {\n        return c === 1 ? \"ден\" : \"дена\";\n      },\n      function (c) {\n        return c === 1 ? \"час\" : \"часа\";\n      },\n      function (c) {\n        return c === 1 ? \"минута\" : \"минути\";\n      },\n      function (c) {\n        return c === 1 ? \"секунда\" : \"секунди\";\n      },\n      function (c) {\n        return c === 1 ? \"милисекунда\" : \"милисекунди\";\n      },\n      \",\"\n    ),\n    mn: language(\n      \"жил\",\n      \"сар\",\n      \"долоо хоног\",\n      \"өдөр\",\n      \"цаг\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    mr: language(\n      function (c) {\n        return c === 1 ? \"वर्ष\" : \"वर्षे\";\n      },\n      function (c) {\n        return c === 1 ? \"महिना\" : \"महिने\";\n      },\n      function (c) {\n        return c === 1 ? \"आठवडा\" : \"आठवडे\";\n      },\n      \"दिवस\",\n      \"तास\",\n      function (c) {\n        return c === 1 ? \"मिनिट\" : \"मिनिटे\";\n      },\n      \"सेकंद\",\n      \"मिलिसेकंद\"\n    ),\n    ms: language(\n      \"tahun\",\n      \"bulan\",\n      \"minggu\",\n      \"hari\",\n      \"jam\",\n      \"minit\",\n      \"saat\",\n      \"milisaat\"\n    ),\n    nl: language(\n      \"jaar\",\n      function (c) {\n        return c === 1 ? \"maand\" : \"maanden\";\n      },\n      function (c) {\n        return c === 1 ? \"week\" : \"weken\";\n      },\n      function (c) {\n        return c === 1 ? \"dag\" : \"dagen\";\n      },\n      \"uur\",\n      function (c) {\n        return c === 1 ? \"minuut\" : \"minuten\";\n      },\n      function (c) {\n        return c === 1 ? \"seconde\" : \"seconden\";\n      },\n      function (c) {\n        return c === 1 ? \"milliseconde\" : \"milliseconden\";\n      },\n      \",\"\n    ),\n    no: language(\n      \"år\",\n      function (c) {\n        return \"måned\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"uke\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"time\" + (c === 1 ? \"\" : \"r\");\n      },\n      function (c) {\n        return \"minutt\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    pl: language(\n      function (c) {\n        return [\"rok\", \"roku\", \"lata\", \"lat\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"miesiąc\", \"miesiąca\", \"miesiące\", \"miesięcy\"][\n          getPolishForm(c)\n        ];\n      },\n      function (c) {\n        return [\"tydzień\", \"tygodnia\", \"tygodnie\", \"tygodni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"dzień\", \"dnia\", \"dni\", \"dni\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"godzina\", \"godziny\", \"godziny\", \"godzin\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][getPolishForm(c)];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][\n          getPolishForm(c)\n        ];\n      },\n      \",\"\n    ),\n    pt: language(\n      function (c) {\n        return \"ano\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return c === 1 ? \"mês\" : \"meses\";\n      },\n      function (c) {\n        return \"semana\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"dia\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"hora\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"minuto\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"segundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      function (c) {\n        return \"milissegundo\" + (c === 1 ? \"\" : \"s\");\n      },\n      \",\"\n    ),\n    ro: language(\n      function (c) {\n        return c === 1 ? \"an\" : \"ani\";\n      },\n      function (c) {\n        return c === 1 ? \"lună\" : \"luni\";\n      },\n      function (c) {\n        return c === 1 ? \"săptămână\" : \"săptămâni\";\n      },\n      function (c) {\n        return c === 1 ? \"zi\" : \"zile\";\n      },\n      function (c) {\n        return c === 1 ? \"oră\" : \"ore\";\n      },\n      function (c) {\n        return c === 1 ? \"minut\" : \"minute\";\n      },\n      function (c) {\n        return c === 1 ? \"secundă\" : \"secunde\";\n      },\n      function (c) {\n        return c === 1 ? \"milisecundă\" : \"milisecunde\";\n      },\n      \",\"\n    ),\n    ru: language(\n      function (c) {\n        return [\"лет\", \"год\", \"года\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месяцев\", \"месяц\", \"месяца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недель\", \"неделя\", \"недели\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дней\", \"день\", \"дня\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"часов\", \"час\", \"часа\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минут\", \"минута\", \"минуты\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунды\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"миллисекунд\", \"миллисекунда\", \"миллисекунды\"][\n          getSlavicForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sq: language(\n      function (c) {\n        return c === 1 ? \"vit\" : \"vjet\";\n      },\n      \"muaj\",\n      \"javë\",\n      \"ditë\",\n      \"orë\",\n      function (c) {\n        return \"minut\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"sekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      function (c) {\n        return \"milisekond\" + (c === 1 ? \"ë\" : \"a\");\n      },\n      \",\"\n    ),\n    sr: language(\n      function (c) {\n        return [\"години\", \"година\", \"године\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"месеци\", \"месец\", \"месеца\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"недељи\", \"недеља\", \"недеље\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"дани\", \"дан\", \"дана\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"сати\", \"сат\", \"сата\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"минута\", \"минут\", \"минута\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунди\", \"секунда\", \"секунде\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"милисекунди\", \"милисекунда\", \"милисекунде\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ta: language(\n      function (c) {\n        return c === 1 ? \"வருடம்\" : \"ஆண்டுகள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மாதம்\" : \"மாதங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"வாரம்\" : \"வாரங்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"நாள்\" : \"நாட்கள்\";\n      },\n      function (c) {\n        return c === 1 ? \"மணி\" : \"மணிநேரம்\";\n      },\n      function (c) {\n        return \"நிமிட\" + (c === 1 ? \"ம்\" : \"ங்கள்\");\n      },\n      function (c) {\n        return \"வினாடி\" + (c === 1 ? \"\" : \"கள்\");\n      },\n      function (c) {\n        return \"மில்லி விநாடி\" + (c === 1 ? \"\" : \"கள்\");\n      }\n    ),\n    te: language(\n      function (c) {\n        return \"సంవత్స\" + (c === 1 ? \"రం\" : \"రాల\");\n      },\n      function (c) {\n        return \"నెల\" + (c === 1 ? \"\" : \"ల\");\n      },\n      function (c) {\n        return c === 1 ? \"వారం\" : \"వారాలు\";\n      },\n      function (c) {\n        return \"రోజు\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return \"గంట\" + (c === 1 ? \"\" : \"లు\");\n      },\n      function (c) {\n        return c === 1 ? \"నిమిషం\" : \"నిమిషాలు\";\n      },\n      function (c) {\n        return c === 1 ? \"సెకను\" : \"సెకన్లు\";\n      },\n      function (c) {\n        return c === 1 ? \"మిల్లీసెకన్\" : \"మిల్లీసెకన్లు\";\n      }\n    ),\n    uk: language(\n      function (c) {\n        return [\"років\", \"рік\", \"роки\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"місяців\", \"місяць\", \"місяці\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"тижнів\", \"тиждень\", \"тижні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"днів\", \"день\", \"дні\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"годин\", \"година\", \"години\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"хвилин\", \"хвилина\", \"хвилини\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"секунд\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n      },\n      function (c) {\n        return [\"мілісекунд\", \"мілісекунда\", \"мілісекунди\"][getSlavicForm(c)];\n      },\n      \",\"\n    ),\n    ur: language(\n      \"سال\",\n      function (c) {\n        return c === 1 ? \"مہینہ\" : \"مہینے\";\n      },\n      function (c) {\n        return c === 1 ? \"ہفتہ\" : \"ہفتے\";\n      },\n      \"دن\",\n      function (c) {\n        return c === 1 ? \"گھنٹہ\" : \"گھنٹے\";\n      },\n      \"منٹ\",\n      \"سیکنڈ\",\n      \"ملی سیکنڈ\"\n    ),\n    sk: language(\n      function (c) {\n        return [\"rok\", \"roky\", \"roky\", \"rokov\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"mesiac\", \"mesiace\", \"mesiace\", \"mesiacov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"týždeň\", \"týždne\", \"týždne\", \"týždňov\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"deň\", \"dni\", \"dni\", \"dní\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"hodina\", \"hodiny\", \"hodiny\", \"hodín\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"minúta\", \"minúty\", \"minúty\", \"minút\"][getCzechOrSlovakForm(c)];\n      },\n      function (c) {\n        return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      function (c) {\n        return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekúnd\"][\n          getCzechOrSlovakForm(c)\n        ];\n      },\n      \",\"\n    ),\n    sl: language(\n      function (c) {\n        if (c % 10 === 1) {\n          return \"leto\";\n        } else if (c % 100 === 2) {\n          return \"leti\";\n        } else if (\n          c % 100 === 3 ||\n          c % 100 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 5)\n        ) {\n          return \"leta\";\n        } else {\n          return \"let\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"mesec\";\n        } else if (c % 100 === 2 || (Math.floor(c) !== c && c % 100 <= 5)) {\n          return \"meseca\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"mesece\";\n        } else {\n          return \"mesecev\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"teden\";\n        } else if (c % 10 === 2 || (Math.floor(c) !== c && c % 100 <= 4)) {\n          return \"tedna\";\n        } else if (c % 10 === 3 || c % 10 === 4) {\n          return \"tedne\";\n        } else {\n          return \"tednov\";\n        }\n      },\n      function (c) {\n        return c % 100 === 1 ? \"dan\" : \"dni\";\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"ura\";\n        } else if (c % 100 === 2) {\n          return \"uri\";\n        } else if (c % 10 === 3 || c % 10 === 4 || Math.floor(c) !== c) {\n          return \"ure\";\n        } else {\n          return \"ur\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"minuta\";\n        } else if (c % 10 === 2) {\n          return \"minuti\";\n        } else if (\n          c % 10 === 3 ||\n          c % 10 === 4 ||\n          (Math.floor(c) !== c && c % 100 <= 4)\n        ) {\n          return \"minute\";\n        } else {\n          return \"minut\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"sekunda\";\n        } else if (c % 100 === 2) {\n          return \"sekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"sekunde\";\n        } else {\n          return \"sekund\";\n        }\n      },\n      function (c) {\n        if (c % 10 === 1) {\n          return \"milisekunda\";\n        } else if (c % 100 === 2) {\n          return \"milisekundi\";\n        } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n          return \"milisekunde\";\n        } else {\n          return \"milisekund\";\n        }\n      },\n      \",\"\n    ),\n    sv: language(\n      \"år\",\n      function (c) {\n        return \"månad\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"veck\" + (c === 1 ? \"a\" : \"or\");\n      },\n      function (c) {\n        return \"dag\" + (c === 1 ? \"\" : \"ar\");\n      },\n      function (c) {\n        return \"timm\" + (c === 1 ? \"e\" : \"ar\");\n      },\n      function (c) {\n        return \"minut\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"sekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      function (c) {\n        return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n      },\n      \",\"\n    ),\n    sw: assign(\n      language(\n        function (c) {\n          return c === 1 ? \"mwaka\" : \"miaka\";\n        },\n        function (c) {\n          return c === 1 ? \"mwezi\" : \"miezi\";\n        },\n        \"wiki\",\n        function (c) {\n          return c === 1 ? \"siku\" : \"masiku\";\n        },\n        function (c) {\n          return c === 1 ? \"saa\" : \"masaa\";\n        },\n        \"dakika\",\n        \"sekunde\",\n        \"milisekunde\"\n      ),\n      { _numberFirst: true }\n    ),\n    tr: language(\n      \"yıl\",\n      \"ay\",\n      \"hafta\",\n      \"gün\",\n      \"saat\",\n      \"dakika\",\n      \"saniye\",\n      \"milisaniye\",\n      \",\"\n    ),\n    th: language(\n      \"ปี\",\n      \"เดือน\",\n      \"สัปดาห์\",\n      \"วัน\",\n      \"ชั่วโมง\",\n      \"นาที\",\n      \"วินาที\",\n      \"มิลลิวินาที\"\n    ),\n    uz: language(\n      \"yil\",\n      \"oy\",\n      \"hafta\",\n      \"kun\",\n      \"soat\",\n      \"minut\",\n      \"sekund\",\n      \"millisekund\"\n    ),\n    uz_CYR: language(\n      \"йил\",\n      \"ой\",\n      \"ҳафта\",\n      \"кун\",\n      \"соат\",\n      \"минут\",\n      \"секунд\",\n      \"миллисекунд\"\n    ),\n    vi: language(\n      \"năm\",\n      \"tháng\",\n      \"tuần\",\n      \"ngày\",\n      \"giờ\",\n      \"phút\",\n      \"giây\",\n      \"mili giây\",\n      \",\"\n    ),\n    zh_CN: language(\"年\", \"个月\", \"周\", \"天\", \"小时\", \"分钟\", \"秒\", \"毫秒\"),\n    zh_TW: language(\"年\", \"個月\", \"周\", \"天\", \"小時\", \"分鐘\", \"秒\", \"毫秒\")\n  };\n\n  /**\n   * Helper function for creating language definitions.\n   *\n   * @internal\n   * @param {Unit} y\n   * @param {Unit} mo\n   * @param {Unit} w\n   * @param {Unit} d\n   * @param {Unit} h\n   * @param {Unit} m\n   * @param {Unit} s\n   * @param {Unit} ms\n   * @param {string} [decimal]\n   * @returns {Language}\n   */\n  function language(y, mo, w, d, h, m, s, ms, decimal) {\n    /** @type {Language} */\n    var result = { y: y, mo: mo, w: w, d: d, h: h, m: m, s: s, ms: ms };\n    if (typeof decimal !== \"undefined\") {\n      result.decimal = decimal;\n    }\n    return result;\n  }\n\n  /**\n   * Helper function for Arabic.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getArabicForm(c) {\n    if (c === 2) {\n      return 1;\n    }\n    if (c > 2 && c < 11) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Polish.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getPolishForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && !(c % 100 > 10 && c % 100 < 20)) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Slavic languages.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getSlavicForm(c) {\n    if (Math.floor(c) !== c) {\n      return 2;\n    }\n    if (\n      (c % 100 >= 5 && c % 100 <= 20) ||\n      (c % 10 >= 5 && c % 10 <= 9) ||\n      c % 10 === 0\n    ) {\n      return 0;\n    }\n    if (c % 10 === 1) {\n      return 1;\n    }\n    if (c > 1) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Czech or Slovak.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getCzechOrSlovakForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && c % 100 < 10) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Lithuanian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getLithuanianForm(c) {\n    if (c === 1 || (c % 10 === 1 && c % 100 > 20)) {\n      return 0;\n    }\n    if (\n      Math.floor(c) !== c ||\n      (c % 10 >= 2 && c % 100 > 20) ||\n      (c % 10 >= 2 && c % 100 < 10)\n    ) {\n      return 1;\n    }\n    return 2;\n  }\n\n  /**\n   * Helper function for Latvian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {boolean}\n   */\n  function getLatvianForm(c) {\n    return c % 10 === 1 && c % 100 !== 11;\n  }\n\n  /**\n   * @internal\n   * @template T\n   * @param {T} obj\n   * @param {keyof T} key\n   * @returns {boolean}\n   */\n  function has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }\n\n  /**\n   * @internal\n   * @param {Pick<Required<Options>, \"language\" | \"fallbacks\" | \"languages\">} options\n   * @throws {Error} Throws an error if language is not found.\n   * @returns {Language}\n   */\n  function getLanguage(options) {\n    var possibleLanguages = [options.language];\n\n    if (has(options, \"fallbacks\")) {\n      if (isArray(options.fallbacks) && options.fallbacks.length) {\n        possibleLanguages = possibleLanguages.concat(options.fallbacks);\n      } else {\n        throw new Error(\"fallbacks must be an array with at least one element\");\n      }\n    }\n\n    for (var i = 0; i < possibleLanguages.length; i++) {\n      var languageToTry = possibleLanguages[i];\n      if (has(options.languages, languageToTry)) {\n        return options.languages[languageToTry];\n      }\n      if (has(LANGUAGES, languageToTry)) {\n        return LANGUAGES[languageToTry];\n      }\n    }\n\n    throw new Error(\"No language found.\");\n  }\n\n  /**\n   * @internal\n   * @param {Piece} piece\n   * @param {Language} language\n   * @param {Pick<Required<Options>, \"decimal\" | \"spacer\" | \"maxDecimalPoints\" | \"digitReplacements\">} options\n   */\n  function renderPiece(piece, language, options) {\n    var unitName = piece.unitName;\n    var unitCount = piece.unitCount;\n\n    var spacer = options.spacer;\n    var maxDecimalPoints = options.maxDecimalPoints;\n\n    /** @type {string} */\n    var decimal;\n    if (has(options, \"decimal\")) {\n      decimal = options.decimal;\n    } else if (has(language, \"decimal\")) {\n      decimal = language.decimal;\n    } else {\n      decimal = \".\";\n    }\n\n    /** @type {undefined | DigitReplacements} */\n    var digitReplacements;\n    if (\"digitReplacements\" in options) {\n      digitReplacements = options.digitReplacements;\n    } else if (\"_digitReplacements\" in language) {\n      digitReplacements = language._digitReplacements;\n    }\n\n    /** @type {string} */\n    var formattedCount;\n    var normalizedUnitCount =\n      maxDecimalPoints === void 0\n        ? unitCount\n        : Math.floor(unitCount * Math.pow(10, maxDecimalPoints)) /\n          Math.pow(10, maxDecimalPoints);\n    var countStr = normalizedUnitCount.toString();\n\n    if (language._hideCountIf2 && unitCount === 2) {\n      formattedCount = \"\";\n      spacer = \"\";\n    } else {\n      if (digitReplacements) {\n        formattedCount = \"\";\n        for (var i = 0; i < countStr.length; i++) {\n          var char = countStr[i];\n          if (char === \".\") {\n            formattedCount += decimal;\n          } else {\n            // @ts-ignore because `char` should always be 0-9 at this point.\n            formattedCount += digitReplacements[char];\n          }\n        }\n      } else {\n        formattedCount = countStr.replace(\".\", decimal);\n      }\n    }\n\n    var languageWord = language[unitName];\n    var word;\n    if (typeof languageWord === \"function\") {\n      word = languageWord(unitCount);\n    } else {\n      word = languageWord;\n    }\n\n    if (language._numberFirst) {\n      return word + spacer + formattedCount;\n    }\n    return formattedCount + spacer + word;\n  }\n\n  /**\n   * @internal\n   * @typedef {Object} Piece\n   * @prop {UnitName} unitName\n   * @prop {number} unitCount\n   */\n\n  /**\n   * @internal\n   * @param {number} ms\n   * @param {Pick<Required<Options>, \"units\" | \"unitMeasures\" | \"largest\" | \"round\">} options\n   * @returns {Piece[]}\n   */\n  function getPieces(ms, options) {\n    /** @type {UnitName} */\n    var unitName;\n\n    /** @type {number} */\n    var i;\n\n    /** @type {number} */\n    var unitCount;\n\n    /** @type {number} */\n    var msRemaining;\n\n    var units = options.units;\n    var unitMeasures = options.unitMeasures;\n    var largest = \"largest\" in options ? options.largest : Infinity;\n\n    if (!units.length) return [];\n\n    // Get the counts for each unit. Doesn't round or truncate anything.\n    // For example, might create an object like `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`.\n    /** @type {Partial<Record<UnitName, number>>} */\n    var unitCounts = {};\n    msRemaining = ms;\n    for (i = 0; i < units.length; i++) {\n      unitName = units[i];\n      var unitMs = unitMeasures[unitName];\n\n      var isLast = i === units.length - 1;\n      unitCount = isLast\n        ? msRemaining / unitMs\n        : Math.floor(msRemaining / unitMs);\n      unitCounts[unitName] = unitCount;\n\n      msRemaining -= unitCount * unitMs;\n    }\n\n    if (options.round) {\n      // Update counts based on the `largest` option.\n      // For example, if `largest === 2` and `unitCount` is `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`,\n      // updates to something like `{ y: 7, m: 6.2 }`.\n      var unitsRemainingBeforeRound = largest;\n      for (i = 0; i < units.length; i++) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        unitsRemainingBeforeRound--;\n\n        // \"Take\" the rest of the units into this one.\n        if (unitsRemainingBeforeRound === 0) {\n          for (var j = i + 1; j < units.length; j++) {\n            var smallerUnitName = units[j];\n            var smallerUnitCount = unitCounts[smallerUnitName];\n            unitCounts[unitName] +=\n              (smallerUnitCount * unitMeasures[smallerUnitName]) /\n              unitMeasures[unitName];\n            unitCounts[smallerUnitName] = 0;\n          }\n          break;\n        }\n      }\n\n      // Round the last piece (which should be the only non-integer).\n      //\n      // This can be a little tricky if the last piece \"bubbles up\" to a larger\n      // unit. For example, \"3 days, 23.99 hours\" should be rounded to \"4 days\".\n      // It can also require multiple passes. For example, \"6 days, 23.99 hours\"\n      // should become \"1 week\".\n      for (i = units.length - 1; i >= 0; i--) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n\n        if (unitCount === 0) continue;\n\n        var rounded = Math.round(unitCount);\n        unitCounts[unitName] = rounded;\n\n        if (i === 0) break;\n\n        var previousUnitName = units[i - 1];\n        var previousUnitMs = unitMeasures[previousUnitName];\n        var amountOfPreviousUnit = Math.floor(\n          (rounded * unitMeasures[unitName]) / previousUnitMs\n        );\n        if (amountOfPreviousUnit) {\n          unitCounts[previousUnitName] += amountOfPreviousUnit;\n          unitCounts[unitName] = 0;\n        } else {\n          break;\n        }\n      }\n    }\n\n    /** @type {Piece[]} */\n    var result = [];\n    for (i = 0; i < units.length && result.length < largest; i++) {\n      unitName = units[i];\n      unitCount = unitCounts[unitName];\n      if (unitCount) {\n        result.push({ unitName: unitName, unitCount: unitCount });\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @internal\n   * @param {Piece[]} pieces\n   * @param {Pick<Required<Options>, \"units\" | \"language\" | \"languages\" | \"fallbacks\" | \"delimiter\" | \"spacer\" | \"decimal\" | \"conjunction\" | \"maxDecimalPoints\" | \"serialComma\" | \"digitReplacements\">} options\n   * @returns {string}\n   */\n  function formatPieces(pieces, options) {\n    var language = getLanguage(options);\n\n    if (!pieces.length) {\n      var units = options.units;\n      var smallestUnitName = units[units.length - 1];\n      return renderPiece(\n        { unitName: smallestUnitName, unitCount: 0 },\n        language,\n        options\n      );\n    }\n\n    var conjunction = options.conjunction;\n    var serialComma = options.serialComma;\n\n    var delimiter;\n    if (has(options, \"delimiter\")) {\n      delimiter = options.delimiter;\n    } else if (has(language, \"delimiter\")) {\n      delimiter = language.delimiter;\n    } else {\n      delimiter = \", \";\n    }\n\n    /** @type {string[]} */\n    var renderedPieces = [];\n    for (var i = 0; i < pieces.length; i++) {\n      renderedPieces.push(renderPiece(pieces[i], language, options));\n    }\n\n    if (!conjunction || pieces.length === 1) {\n      return renderedPieces.join(delimiter);\n    }\n\n    if (pieces.length === 2) {\n      return renderedPieces.join(conjunction);\n    }\n\n    return (\n      renderedPieces.slice(0, -1).join(delimiter) +\n      (serialComma ? \",\" : \"\") +\n      conjunction +\n      renderedPieces.slice(-1)\n    );\n  }\n\n  /**\n   * Create a humanizer, which lets you change the default options.\n   *\n   * @param {Options} [passedOptions]\n   */\n  function humanizer(passedOptions) {\n    /**\n     * @param {number} ms\n     * @param {Options} [humanizerOptions]\n     * @returns {string}\n     */\n    var result = function humanizer(ms, humanizerOptions) {\n      // Make sure we have a positive number.\n      //\n      // Has the nice side-effect of converting things to numbers. For example,\n      // converts `\"123\"` and `Number(123)` to `123`.\n      ms = Math.abs(ms);\n\n      var options = assign({}, result, humanizerOptions || {});\n\n      var pieces = getPieces(ms, options);\n\n      return formatPieces(pieces, options);\n    };\n\n    return assign(\n      result,\n      {\n        language: \"en\",\n        spacer: \" \",\n        conjunction: \"\",\n        serialComma: true,\n        units: [\"y\", \"mo\", \"w\", \"d\", \"h\", \"m\", \"s\"],\n        languages: {},\n        round: false,\n        unitMeasures: {\n          y: 31557600000,\n          mo: 2629800000,\n          w: 604800000,\n          d: 86400000,\n          h: 3600000,\n          m: 60000,\n          s: 1000,\n          ms: 1\n        }\n      },\n      passedOptions\n    );\n  }\n\n  /**\n   * Humanize a duration.\n   *\n   * This is a wrapper around the default humanizer.\n   */\n  var humanizeDuration = assign(humanizer({}), {\n    getSupportedLanguages: function getSupportedLanguages() {\n      var result = [];\n      for (var language in LANGUAGES) {\n        if (has(LANGUAGES, language) && language !== \"gr\") {\n          result.push(language);\n        }\n      }\n      return result;\n    },\n    humanizer: humanizer\n  });\n\n  // @ts-ignore\n  if (typeof define === \"function\" && define.amd) {\n    // @ts-ignore\n    define(function () {\n      return humanizeDuration;\n    });\n  } else if (typeof module !== \"undefined\" && module.exports) {\n    module.exports = humanizeDuration;\n  } else {\n    this.humanizeDuration = humanizeDuration;\n  }\n})();\n"], "mappings": ";;;;;AAAA;AAAA;AAqEA,KAAC,WAAY;AAEX,UAAI,SACF,OAAO;AAAA,MAEP,SAAU,aAAa;AACrB,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,mBAAS,UAAU,CAAC;AACpB,mBAAS,QAAQ,QAAQ;AACvB,gBAAI,IAAI,QAAQ,IAAI,GAAG;AACrB,0BAAY,IAAI,IAAI,OAAO,IAAI;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAGF,UAAI,UACF,MAAM,WACN,SAAU,KAAK;AACb,eAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,MACjD;AAMF,UAAI,QAAQ;AAAA,QACV,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,aAAa;AAAA,QAChC;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B;AAAA,QACA,SAAU,GAAG;AACX,iBAAO,MAAM,IAAI,iBAAiB;AAAA,QACpC;AAAA,QACA,SAAU,GAAG;AACX,kBAAQ,MAAM,IAAI,aAAa,cAAc;AAAA,QAC/C;AAAA,QACA;AAAA,MACF;AAMA,UAAI,YAAY;AAAA,QACd,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI,SAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,OAAO,QAAQ,QAAQ;AAAA,QACtE,IAAI;AAAA,UACF;AAAA,YACE,SAAU,GAAG;AACX,qBAAO,CAAC,OAAO,SAAS,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,YACnD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,OAAO,SAAS,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,YAClD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,SAAS,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,YACxD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,OAAO,SAAS,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,YAClD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,QAAQ,UAAU,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,YACrD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,SAAS,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,YACvD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,SAAS,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,YACvD;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,CAAC,kBAAkB,mBAAmB,kBAAkB,EAC7D,cAAc,CAAC,CACjB;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,oBAAoB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,UACvE;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACvD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC3D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,OAAO,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,UAC/C;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UACjD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC3D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,UACvE;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,QAAQ,MAAM,IAAI,MAAM;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,MAAM,IAAI,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,UAC/D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,UAAU,UAAU,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACrE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,UAC7D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAC/C,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/D,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,aAAa;AAAA,UAChC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,cAAc,MAAM,IAAI,KAAK;AAAA,UACtC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,YAAY;AAAA,UAC/B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,aAAa;AAAA,UAChC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,QAAQ,KAAK,IAAI,MAAM;AAAA,UAChC;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,KAAK,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,KAAK,IAAI,MAAM;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,KAAK,IAAI,MAAM;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,KAAK,IAAI,MAAM;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,KAAK,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,kBAAkB,KAAK,IAAI,MAAM;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,cAAc;AAAA,UACjC;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAChD,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,MAAM,GAAG;AACX,qBAAO;AAAA,YACT,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACxC,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,KAAK,MAAM,IAAI;AAC5B,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,MAAM,GAAG;AACX,qBAAO;AAAA,YACT,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACxC,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,QAAQ,IAAI;AAChB,iBAAK,UAAU,KAAK,UAAU,KAAK,UAAU,OAAO,IAAI,MAAM,IAAI,KAAK;AACrE,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,QAAQ,IAAI;AAChB,gBAAI,UAAU,KAAM,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,IAAK;AAC9D,qBAAO;AAAA,YACT,WAAW,UAAU,GAAG;AACtB,qBAAO;AAAA,YACT,WAAW,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;AACpD,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,MAAM,GAAG;AACX,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvD,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,OAAO;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,OAAO;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,MAAM,IAAI,MAAM;AAAA,UACxC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,MAAM;AAAA,UAC1C;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,cAAc,MAAM,IAAI,MAAM;AAAA,UACvC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,QAAQ,MAAM,IAAI,MAAM;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,MAAM;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACvD,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,YAAY;AAAA,UAC/B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,gBAAgB;AAAA,UACnC;AAAA,QACF;AAAA,QACA,IAAI,SAAS,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QACzD,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,IAAI,OAAO,KAAM,IAAI,OAAO,MAAM,IAAI,OAAO,KAChD,SACA;AAAA,UACN;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,YAAY,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,UAC9D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,YAAY,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAAA,UACjE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,UAAU,OAAO,EAAE,kBAAkB,CAAC,CAAC;AAAA,UAC1D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,YAAY,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,UAChE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,UAC9D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,YAAY,WAAW,EAAE,kBAAkB,CAAC,CAAC;AAAA,UAClE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,gBAAgB,eAAe,EACpD,kBAAkB,CAAC,CACrB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,SAAS;AAAA,UACtC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,YAAY;AAAA,UACzC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,UACxC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,UAAU;AAAA,UACvC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,UACxC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,UACxC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,YAAY;AAAA,UACzC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,eAAe,CAAC,IAAI,gBAAgB;AAAA,UAC7C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,YAAY;AAAA,UAC/B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,gBAAgB;AAAA,UACnC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,YAAY;AAAA,UAC/B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,iBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,YAAY,YAAY,UAAU,EACnD,cAAc,CAAC,CACjB;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,YAAY,YAAY,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,QAAQ,OAAO,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,UACzD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACrE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,UACjE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACrE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/D,cAAc,CAAC,CACjB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,UAC1C;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,OAAO;AAAA,UAC1B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,cAAc;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,OAAO;AAAA,UAC1B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,YAAY;AAAA,UAC/B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,gBAAgB;AAAA,UACnC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UAChD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,UACjD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UAClD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACvD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC1D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,gBAAgB,cAAc,EACnD,cAAc,CAAC,CACjB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,gBAAgB,MAAM,IAAI,MAAM;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACvD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UACjD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UACjD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACvD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC3D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,UACvE;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,QAAQ;AAAA,UAC3B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,OAAO;AAAA,UACrC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,mBAAmB,MAAM,IAAI,KAAK;AAAA,UAC3C;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,OAAO;AAAA,UACtC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,UAClC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,WAAW;AAAA,UAC9B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,gBAAgB;AAAA,UACnC;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,UAClD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACzD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,UACxD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,UACjD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,SAAS,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,UACvD;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC1D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,UAC1D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,cAAc,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,UACtE;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,QAAQ,QAAQ,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACjE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,WAAW,WAAW,UAAU,EAChD,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,SAAS,EAC7C,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,UAC7D;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,UACxE;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAC/C,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAC/D,qBAAqB,CAAC,CACxB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,GAAG;AACxB,qBAAO;AAAA,YACT,WACE,IAAI,QAAQ,KACZ,IAAI,QAAQ,KACX,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GACnC;AACA,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,KAAM,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAI;AACjE,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvC,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,KAAM,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAI;AAChE,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvC,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,IAAI,QAAQ,IAAI,QAAQ;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,GAAG;AACxB,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAC9D,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,OAAO,GAAG;AACvB,qBAAO;AAAA,YACT,WACE,IAAI,OAAO,KACX,IAAI,OAAO,KACV,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GACnC;AACA,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,GAAG;AACxB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAChE,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,gBAAI,IAAI,OAAO,GAAG;AAChB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,GAAG;AACxB,qBAAO;AAAA,YACT,WAAW,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAChE,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,MAAM;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,UACjC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,UAAU,MAAM,IAAI,MAAM;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,UACnC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,UACpC;AAAA,UACA,SAAU,GAAG;AACX,mBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,YACE,SAAU,GAAG;AACX,qBAAO,MAAM,IAAI,UAAU;AAAA,YAC7B;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,MAAM,IAAI,UAAU;AAAA,YAC7B;AAAA,YACA;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,MAAM,IAAI,SAAS;AAAA,YAC5B;AAAA,YACA,SAAU,GAAG;AACX,qBAAO,MAAM,IAAI,QAAQ;AAAA,YAC3B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,EAAE,cAAc,KAAK;AAAA,QACvB;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;AAAA,QAC1D,OAAO,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;AAAA,MAC5D;AAiBA,eAAS,SAAS,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,SAAS;AAEnD,YAAI,SAAS,EAAE,GAAM,IAAQ,GAAM,GAAM,GAAM,GAAM,GAAM,GAAO;AAClE,YAAI,OAAO,YAAY,aAAa;AAClC,iBAAO,UAAU;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,KAAK,IAAI,IAAI;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK;AACjE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YACG,IAAI,OAAO,KAAK,IAAI,OAAO,MAC3B,IAAI,MAAM,KAAK,IAAI,MAAM,KAC1B,IAAI,OAAO,GACX;AACA,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,OAAO,GAAG;AAChB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,GAAG;AACT,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,qBAAqB,GAAG;AAC/B,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,IAAI;AAC9C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,kBAAkB,GAAG;AAC5B,YAAI,MAAM,KAAM,IAAI,OAAO,KAAK,IAAI,MAAM,IAAK;AAC7C,iBAAO;AAAA,QACT;AACA,YACE,KAAK,MAAM,CAAC,MAAM,KACjB,IAAI,MAAM,KAAK,IAAI,MAAM,MACzB,IAAI,MAAM,KAAK,IAAI,MAAM,IAC1B;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,eAAe,GAAG;AACzB,eAAO,IAAI,OAAO,KAAK,IAAI,QAAQ;AAAA,MACrC;AASA,eAAS,IAAI,KAAK,KAAK;AACrB,eAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAAA,MACtD;AAQA,eAAS,YAAY,SAAS;AAC5B,YAAI,oBAAoB,CAAC,QAAQ,QAAQ;AAEzC,YAAI,IAAI,SAAS,WAAW,GAAG;AAC7B,cAAI,QAAQ,QAAQ,SAAS,KAAK,QAAQ,UAAU,QAAQ;AAC1D,gCAAoB,kBAAkB,OAAO,QAAQ,SAAS;AAAA,UAChE,OAAO;AACL,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UACxE;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,cAAI,gBAAgB,kBAAkB,CAAC;AACvC,cAAI,IAAI,QAAQ,WAAW,aAAa,GAAG;AACzC,mBAAO,QAAQ,UAAU,aAAa;AAAA,UACxC;AACA,cAAI,IAAI,WAAW,aAAa,GAAG;AACjC,mBAAO,UAAU,aAAa;AAAA,UAChC;AAAA,QACF;AAEA,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AAQA,eAAS,YAAY,OAAOA,WAAU,SAAS;AAC7C,YAAI,WAAW,MAAM;AACrB,YAAI,YAAY,MAAM;AAEtB,YAAI,SAAS,QAAQ;AACrB,YAAI,mBAAmB,QAAQ;AAG/B,YAAI;AACJ,YAAI,IAAI,SAAS,SAAS,GAAG;AAC3B,oBAAU,QAAQ;AAAA,QACpB,WAAW,IAAIA,WAAU,SAAS,GAAG;AACnC,oBAAUA,UAAS;AAAA,QACrB,OAAO;AACL,oBAAU;AAAA,QACZ;AAGA,YAAI;AACJ,YAAI,uBAAuB,SAAS;AAClC,8BAAoB,QAAQ;AAAA,QAC9B,WAAW,wBAAwBA,WAAU;AAC3C,8BAAoBA,UAAS;AAAA,QAC/B;AAGA,YAAI;AACJ,YAAI,sBACF,qBAAqB,SACjB,YACA,KAAK,MAAM,YAAY,KAAK,IAAI,IAAI,gBAAgB,CAAC,IACrD,KAAK,IAAI,IAAI,gBAAgB;AACnC,YAAI,WAAW,oBAAoB,SAAS;AAE5C,YAAIA,UAAS,iBAAiB,cAAc,GAAG;AAC7C,2BAAiB;AACjB,mBAAS;AAAA,QACX,OAAO;AACL,cAAI,mBAAmB;AACrB,6BAAiB;AACjB,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAI,OAAO,SAAS,CAAC;AACrB,kBAAI,SAAS,KAAK;AAChB,kCAAkB;AAAA,cACpB,OAAO;AAEL,kCAAkB,kBAAkB,IAAI;AAAA,cAC1C;AAAA,YACF;AAAA,UACF,OAAO;AACL,6BAAiB,SAAS,QAAQ,KAAK,OAAO;AAAA,UAChD;AAAA,QACF;AAEA,YAAI,eAAeA,UAAS,QAAQ;AACpC,YAAI;AACJ,YAAI,OAAO,iBAAiB,YAAY;AACtC,iBAAO,aAAa,SAAS;AAAA,QAC/B,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,YAAIA,UAAS,cAAc;AACzB,iBAAO,OAAO,SAAS;AAAA,QACzB;AACA,eAAO,iBAAiB,SAAS;AAAA,MACnC;AAeA,eAAS,UAAU,IAAI,SAAS;AAE9B,YAAI;AAGJ,YAAI;AAGJ,YAAI;AAGJ,YAAI;AAEJ,YAAI,QAAQ,QAAQ;AACpB,YAAI,eAAe,QAAQ;AAC3B,YAAI,UAAU,aAAa,UAAU,QAAQ,UAAU;AAEvD,YAAI,CAAC,MAAM,OAAQ,QAAO,CAAC;AAK3B,YAAI,aAAa,CAAC;AAClB,sBAAc;AACd,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,qBAAW,MAAM,CAAC;AAClB,cAAI,SAAS,aAAa,QAAQ;AAElC,cAAI,SAAS,MAAM,MAAM,SAAS;AAClC,sBAAY,SACR,cAAc,SACd,KAAK,MAAM,cAAc,MAAM;AACnC,qBAAW,QAAQ,IAAI;AAEvB,yBAAe,YAAY;AAAA,QAC7B;AAEA,YAAI,QAAQ,OAAO;AAIjB,cAAI,4BAA4B;AAChC,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,uBAAW,MAAM,CAAC;AAClB,wBAAY,WAAW,QAAQ;AAE/B,gBAAI,cAAc,EAAG;AAErB;AAGA,gBAAI,8BAA8B,GAAG;AACnC,uBAAS,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACzC,oBAAI,kBAAkB,MAAM,CAAC;AAC7B,oBAAI,mBAAmB,WAAW,eAAe;AACjD,2BAAW,QAAQ,KAChB,mBAAmB,aAAa,eAAe,IAChD,aAAa,QAAQ;AACvB,2BAAW,eAAe,IAAI;AAAA,cAChC;AACA;AAAA,YACF;AAAA,UACF;AAQA,eAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,uBAAW,MAAM,CAAC;AAClB,wBAAY,WAAW,QAAQ;AAE/B,gBAAI,cAAc,EAAG;AAErB,gBAAI,UAAU,KAAK,MAAM,SAAS;AAClC,uBAAW,QAAQ,IAAI;AAEvB,gBAAI,MAAM,EAAG;AAEb,gBAAI,mBAAmB,MAAM,IAAI,CAAC;AAClC,gBAAI,iBAAiB,aAAa,gBAAgB;AAClD,gBAAI,uBAAuB,KAAK;AAAA,cAC7B,UAAU,aAAa,QAAQ,IAAK;AAAA,YACvC;AACA,gBAAI,sBAAsB;AACxB,yBAAW,gBAAgB,KAAK;AAChC,yBAAW,QAAQ,IAAI;AAAA,YACzB,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,YAAI,SAAS,CAAC;AACd,aAAK,IAAI,GAAG,IAAI,MAAM,UAAU,OAAO,SAAS,SAAS,KAAK;AAC5D,qBAAW,MAAM,CAAC;AAClB,sBAAY,WAAW,QAAQ;AAC/B,cAAI,WAAW;AACb,mBAAO,KAAK,EAAE,UAAoB,UAAqB,CAAC;AAAA,UAC1D;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAAS,aAAa,QAAQ,SAAS;AACrC,YAAIA,YAAW,YAAY,OAAO;AAElC,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,QAAQ,QAAQ;AACpB,cAAI,mBAAmB,MAAM,MAAM,SAAS,CAAC;AAC7C,iBAAO;AAAA,YACL,EAAE,UAAU,kBAAkB,WAAW,EAAE;AAAA,YAC3CA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,YAAI,cAAc,QAAQ;AAC1B,YAAI,cAAc,QAAQ;AAE1B,YAAI;AACJ,YAAI,IAAI,SAAS,WAAW,GAAG;AAC7B,sBAAY,QAAQ;AAAA,QACtB,WAAW,IAAIA,WAAU,WAAW,GAAG;AACrC,sBAAYA,UAAS;AAAA,QACvB,OAAO;AACL,sBAAY;AAAA,QACd;AAGA,YAAI,iBAAiB,CAAC;AACtB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,yBAAe,KAAK,YAAY,OAAO,CAAC,GAAGA,WAAU,OAAO,CAAC;AAAA,QAC/D;AAEA,YAAI,CAAC,eAAe,OAAO,WAAW,GAAG;AACvC,iBAAO,eAAe,KAAK,SAAS;AAAA,QACtC;AAEA,YAAI,OAAO,WAAW,GAAG;AACvB,iBAAO,eAAe,KAAK,WAAW;AAAA,QACxC;AAEA,eACE,eAAe,MAAM,GAAG,EAAE,EAAE,KAAK,SAAS,KACzC,cAAc,MAAM,MACrB,cACA,eAAe,MAAM,EAAE;AAAA,MAE3B;AAOA,eAAS,UAAU,eAAe;AAMhC,YAAI,SAAS,SAASC,WAAU,IAAI,kBAAkB;AAKpD,eAAK,KAAK,IAAI,EAAE;AAEhB,cAAI,UAAU,OAAO,CAAC,GAAG,QAAQ,oBAAoB,CAAC,CAAC;AAEvD,cAAI,SAAS,UAAU,IAAI,OAAO;AAElC,iBAAO,aAAa,QAAQ,OAAO;AAAA,QACrC;AAEA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,aAAa;AAAA,YACb,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,YAC1C,WAAW,CAAC;AAAA,YACZ,OAAO;AAAA,YACP,cAAc;AAAA,cACZ,GAAG;AAAA,cACH,IAAI;AAAA,cACJ,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,IAAI;AAAA,YACN;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAOA,UAAI,mBAAmB,OAAO,UAAU,CAAC,CAAC,GAAG;AAAA,QAC3C,uBAAuB,SAAS,wBAAwB;AACtD,cAAI,SAAS,CAAC;AACd,mBAASD,aAAY,WAAW;AAC9B,gBAAI,IAAI,WAAWA,SAAQ,KAAKA,cAAa,MAAM;AACjD,qBAAO,KAAKA,SAAQ;AAAA,YACtB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE9C,eAAO,WAAY;AACjB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,WAAW,OAAO,WAAW,eAAe,OAAO,SAAS;AAC1D,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,GAAG;AAAA;AAAA;", "names": ["language", "humanizer"]}